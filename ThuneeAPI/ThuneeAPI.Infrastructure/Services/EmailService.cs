using Microsoft.Extensions.Configuration;
using System;
using System.Net.Mail;
using System.Net;
using System.Threading.Tasks;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Infrastructure.Services
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task SendEmailAsync(string to, string subject, string body)
        {
            try
            {
                // Check if email settings are configured
                var host = _configuration["EmailSettings:Host"];
                var portStr = _configuration["EmailSettings:Port"];
                var useSslStr = _configuration["EmailSettings:UseSSL"];
                var username = _configuration["EmailSettings:Username"];
                var password = _configuration["EmailSettings:Password"];
                var from = _configuration["EmailSettings:From"];
                var displayName = _configuration["EmailSettings:DisplayName"];

                if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(portStr) ||
                    string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password) ||
                    string.IsNullOrEmpty(from))
                {
                    Console.WriteLine("Email settings not configured properly. Skipping email send.");
                    return;
                }

                if (!int.TryParse(portStr, out int port))
                {
                    Console.WriteLine($"Invalid port configuration: {portStr}. Skipping email send.");
                    return;
                }

                if (!bool.TryParse(useSslStr, out bool useSSL))
                {
                    useSSL = true; // Default to true for security
                }

                var smtpClient = new SmtpClient
                {
                    Host = host,
                    Port = port,
                    EnableSsl = useSSL,
                    Credentials = new NetworkCredential(username, password)
                };

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(from, displayName ?? "Thunee Competition"),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };
                mailMessage.To.Add(to);

                await smtpClient.SendMailAsync(mailMessage);
                Console.WriteLine($"Email sent successfully to {to}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending email to {to}: {ex.Message}");
                // In production, you might want to log this properly or throw the exception
                // For now, we'll just log and continue to not break the registration flow
            }
        }

        public async Task SendWelcomeEmailAsync(string to, string username)
        {
            var subject = "Welcome to Thunee Competition!";
            var body = GetWelcomeEmailBody(username);
            await SendEmailAsync(to, subject, body);
        }

        public async Task SendTeamPairingEmailAsync(string to, string username, string teamName, string partnerName)
        {
            var subject = "Team Pairing Complete - Ready to Compete!";
            var body = GetTeamPairingEmailBody(username, teamName, partnerName);
            await SendEmailAsync(to, subject, body);
        }

        private static string GetWelcomeEmailBody(string username)
        {
            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='background: linear-gradient(135deg, #E1C760, #D4AF37); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                            <h1 style='color: #000; margin: 0; font-size: 28px;'>Welcome to Thunee!</h1>
                        </div>
                        
                        <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                            <h2 style='color: #E1C760; margin-top: 0;'>Hello {username}!</h2>
                            
                            <p>Welcome to the exciting world of Thunee competitions! We're thrilled to have you join our community of skilled card game players.</p>
                            
                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #E1C760;'>
                                <h3 style='color: #333; margin-top: 0;'>What's Next?</h3>
                                <ul style='color: #666;'>
                                    <li>Find a partner and create or join a competition team</li>
                                    <li>Participate in exciting tournaments and challenges</li>
                                    <li>Climb the leaderboards and earn prizes</li>
                                    <li>Connect with fellow Thunee enthusiasts</li>
                                </ul>
                            </div>
                            
                            <p>Ready to start your Thunee journey? Log in to your account and explore the available competitions!</p>
                            
                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='#' style='background: #E1C760; color: #000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;'>Start Playing</a>
                            </div>
                            
                            <p style='color: #666; font-size: 14px; margin-top: 30px;'>
                                If you have any questions, feel free to reach out to our support team. Good luck and have fun!
                            </p>
                        </div>
                        
                        <div style='text-align: center; padding: 20px; color: #666; font-size: 12px;'>
                            <p>© 2025 Thunee Competition Platform. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GetTeamPairingEmailBody(string username, string teamName, string partnerName)
        {
            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='background: linear-gradient(135deg, #E1C760, #D4AF37); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                            <h1 style='color: #000; margin: 0; font-size: 28px;'>Team Complete!</h1>
                        </div>
                        
                        <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                            <h2 style='color: #E1C760; margin-top: 0;'>Congratulations {username}!</h2>
                            
                            <p>Great news! Your team <strong>{teamName}</strong> is now complete and ready to compete!</p>
                            
                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #E1C760;'>
                                <h3 style='color: #333; margin-top: 0;'>Team Details</h3>
                                <p style='margin: 10px 0;'><strong>Team Name:</strong> {teamName}</p>
                                <p style='margin: 10px 0;'><strong>Your Partner:</strong> {partnerName}</p>
                                <p style='margin: 10px 0;'><strong>Status:</strong> Ready to compete!</p>
                            </div>
                            
                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>
                                <h3 style='color: #333; margin-top: 0;'>Ready to Play?</h3>
                                <ul style='color: #666;'>
                                    <li>Create a game lobby and invite your opponents</li>
                                    <li>Practice your strategies with your partner</li>
                                    <li>Compete in tournaments and climb the leaderboards</li>
                                    <li>Earn points and win exciting prizes</li>
                                </ul>
                            </div>
                            
                            <p>Your team is now eligible to participate in all available competitions. Good luck and may the best team win!</p>
                            
                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='#' style='background: #E1C760; color: #000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;'>Start Competing</a>
                            </div>
                            
                            <p style='color: #666; font-size: 14px; margin-top: 30px;'>
                                Best of luck to you and {partnerName}! Show everyone what {teamName} is made of!
                            </p>
                        </div>
                        
                        <div style='text-align: center; padding: 20px; color: #666; font-size: 12px;'>
                            <p>© 2025 Thunee Competition Platform. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>";
        }
    }
}
